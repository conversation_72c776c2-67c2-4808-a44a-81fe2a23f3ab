<template>
  <el-dialog
    v-model="visible"
    title="一键图表排序"
    width="500px"
    center
    :show-close="!loading"
    :close-on-click-modal="!loading"
    :close-on-press-escape="!loading"
    :before-close="handleBeforeClose"
    @close="handleCancel"
  >
    <!-- 功能说明 -->
    <div class="sort-subtitle">对整篇文件图表、表格等按正确的顺序标注序号</div>

    <!-- 处理状态显示 -->
    <div class="sort-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-text">标注中</div>
        <div class="loading-icon">
          <el-icon class="is-loading" size="32">
            <Loading style="color: #5b69e5" />
          </el-icon>
        </div>
      </div>

      <!-- 完成状态 -->
      <div v-else-if="completed" class="completed-state">
        <div class="success-icon">
          <el-icon :color="statusColor" size="48">
            <component :is="statusIcon" />
          </el-icon>
        </div>
        <div class="success-text">{{ statusText }}</div>
        <div class="success-desc" v-if="result">
          {{ result.message }}
        </div>
        <div class="result-details" v-if="result && result.success">
          <div class="detail-item">
            <span class="detail-label">图类元素：</span>
            <span class="detail-value">{{ result.figureCount }} 个</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">表类元素：</span>
            <span class="detail-value">{{ result.tableCount }} 个</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button
          v-if="!completed"
          type="primary"
          @click="handleStart"
          :loading="loading"
          :disabled="loading"
        >
          {{ loading ? '处理中...' : '确认' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref, watch, computed } from 'vue'
import { Loading, SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import type { ChartSortResult } from '@/utils/chartSortUtils'

const props = defineProps<{
  modelValue: boolean
  loading?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'start'): void
  (e: 'complete', result: ChartSortResult): void
}>()

// 内部状态
const loading = computed(() => props.loading || false)
const completed = ref(false)
const result = ref<ChartSortResult | null>(null)

// 计算属性控制弹窗显示
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 状态图标
const statusIcon = computed(() => {
  return result.value?.success ? SuccessFilled : CircleCloseFilled
})

// 状态颜色
const statusColor = computed(() => {
  return result.value?.success ? '#67C23A' : '#F56C6C'
})

// 状态文案
const statusText = computed(() => {
  return result.value?.success ? '已完成标注' : '排序错误'
})

// 监听 modelValue 变化，当弹窗打开时重置状态
watch(
  () => props.modelValue,
  async (newValue) => {
    if (newValue) {
      // 重置状态
      completed.value = false
      result.value = null
    }
  }
)

// 监听 loading 状态变化
watch(
  () => props.loading,
  (newLoading) => {
    if (!newLoading && result.value) {
      // 加载完成，显示结果
      completed.value = true
    }
  }
)

// 处理弹窗关闭前的检查
const handleBeforeClose = (done: () => void) => {
  if (loading.value) {
    ElMessage.warning('正在处理图表排序，请稍候')
    return
  }
  done()
}

// 开始排序
const handleStart = async () => {
  if (loading.value) {
    ElMessage.warning('正在处理图表排序，请稍候')
    return
  }
  emit('start')
}

// 取消/关闭
const handleCancel = () => {
  if (loading.value) {
    ElMessage.warning('正在处理图表排序，请稍候')
    return
  }
  emit('update:modelValue', false)
}

// 设置结果（由父组件调用）
const setResult = (sortResult: ChartSortResult) => {
  result.value = sortResult
  if (!loading.value) {
    completed.value = true
  }
}

// 暴露方法给父组件
defineExpose({
  setResult
})
</script>

<style lang="scss" scoped>
.sort-subtitle {
  color: #999999;
  font-size: 15px;
  margin-top: 10px;
  margin-bottom: 20px;
  text-align: center;
}

.sort-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.loading-state,
.initial-state {
  display: flex;
  //   flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.completed-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.loading-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

.is-loading {
  line-height: 25px;
}

.loading-text,
.success-text,
.info-text {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.loading-desc,
.success-desc,
.info-desc {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  max-width: 400px;
}

.result-details {
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 6px;
  min-width: 200px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  color: #606266;
  font-size: 14px;
}

.detail-value {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}
</style>
